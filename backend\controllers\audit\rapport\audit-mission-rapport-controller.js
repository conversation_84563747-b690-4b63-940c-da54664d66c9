const { sequelize } = require('../../../models/index');
const db = require('../../../models');
const AuditMissionRapport = require('../../../models/Audit/rapport/auditmissionrapport')(sequelize);
const { AuditMissionRapportSupport, AuditMission } = db;
const { Document, Packer, Paragraph, TextRun, HeadingLevel, Table, TableRow, TableCell, WidthType, BorderStyle, Header, Footer, AlignmentType, ImageRun } = require('docx');
const PDFDocument = require('pdfkit');

const escapeText = (str) => {
  if (typeof str !== 'string') return str || 'N/A';
  return str.replace(/&/g, '&amp;').replace(/</g, '&lt;').replace(/>/g, '&gt;');
};

// Helper function to add logo to PDF pages
const addLogoToPage = (doc, logoBase64, x = 50, y = 50, width = 100, height = 50) => {
  if (logoBase64 && logoBase64.trim() !== '') {
    try {
      console.log('Adding logo to PDF at position:', x, y);
      // Remove data URL prefix if present
      const base64Data = logoBase64.replace(/^data:image\/[a-z]+;base64,/, '');

      // Validate base64 data
      if (base64Data && base64Data.length > 0) {
        const logoBuffer = Buffer.from(base64Data, 'base64');
        doc.image(logoBuffer, x, y, { width, height });
        console.log('Logo added successfully');
      } else {
        console.log('Logo base64 data is empty');
      }
    } catch (error) {
      console.error('Error adding logo to PDF:', error);
      // Add a placeholder text instead
      doc.fontSize(10).fillColor('#999999');
      doc.text('[LOGO]', x, y);
    }
  } else {
    console.log('No logo provided');
  }
};

// Helper function to add electronic signature to PDF
const addSignatureToPage = (doc, signatureBase64, x, y, width = 150, height = 75) => {
  if (signatureBase64 && signatureBase64.trim() !== '') {
    try {
      console.log('Adding signature to PDF at position:', x, y);
      // Remove data URL prefix if present
      const base64Data = signatureBase64.replace(/^data:image\/[a-z]+;base64,/, '');

      // Validate base64 data
      if (base64Data && base64Data.length > 0) {
        const signatureBuffer = Buffer.from(base64Data, 'base64');
        doc.image(signatureBuffer, x, y, { width, height });
        console.log('Signature added successfully');
      } else {
        console.log('Signature base64 data is empty');
      }
    } catch (error) {
      console.error('Error adding signature to PDF:', error);
      // Add a placeholder text instead
      doc.fontSize(10).fillColor('#999999');
      doc.text('[SIGNATURE]', x, y);
    }
  } else {
    console.log('No signature provided');
  }
};

// Helper function to safely process image data for DOCX with robust validation
const processImageForDocx = (base64String, imageName = 'image') => {
  try {
    if (!base64String || typeof base64String !== 'string') {
      console.warn(`[DOCX] ${imageName} - Invalid base64 string provided`);
      return null;
    }

    // Remove data URL prefix if present and validate format
    let base64Data = base64String;
    if (base64String.includes(';base64,')) {
      const parts = base64String.split(';base64,');
      if (parts.length !== 2) {
        console.warn(`[DOCX] ${imageName} - Invalid data URL format`);
        return null;
      }
      base64Data = parts[1];
    }

    // Validate base64 data
    if (!base64Data || base64Data.length === 0) {
      console.warn(`[DOCX] ${imageName} - Empty base64 data`);
      return null;
    }

    // Additional validation: check if it's valid base64
    const base64Regex = /^[A-Za-z0-9+/]*={0,2}$/;
    if (!base64Regex.test(base64Data)) {
      console.warn(`[DOCX] ${imageName} - Invalid base64 characters`);
      return null;
    }

    // Convert to buffer with error handling
    const buffer = Buffer.from(base64Data, 'base64');

    // Validate buffer size (reasonable limits)
    if (buffer.length === 0) {
      console.warn(`[DOCX] ${imageName} - Empty buffer generated`);
      return null;
    }

    if (buffer.length > 10 * 1024 * 1024) { // 10MB limit
      console.warn(`[DOCX] ${imageName} - Image too large (${buffer.length} bytes)`);
      return null;
    }

    console.log(`[DOCX] ${imageName} - Successfully processed (${buffer.length} bytes)`);
    return buffer;
  } catch (error) {
    console.error(`[DOCX] ${imageName} - Processing error:`, error.message);
    return null;
  }
};

// Helper function to format date
const formatDate = (dateString) => {
  if (!dateString) return 'N/A';
  try {
    const date = new Date(dateString);
    return date.toLocaleDateString('fr-FR', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  } catch (error) {
    return dateString;
  }
};

// Generate enhanced mission report with front page and sommaire
exports.getEnhancedMissionReport = async (req, res) => {
  const { missionId } = req.params;
  const { format } = req.query;

  // Set CORS headers immediately
  res.set({
    'Access-Control-Allow-Origin': 'http://localhost:5173',
    'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Requested-With, x-user-id',
    'Access-Control-Allow-Credentials': 'true'
  });

  console.log('[EnhancedAuditMissionRapport] Processing request for mission:', missionId, 'format:', format);

  try {
    // Get mission data
    const mission = await AuditMission.findByPk(missionId);
    if (!mission) {
      return res.status(404).json({ message: 'Mission not found' });
    }

    // Get rapport support data
    const rapportSupport = await AuditMissionRapportSupport.findOne({
      where: { auditMissionId: missionId }
    });

    console.log('Rapport support found:', rapportSupport ? 'Yes' : 'No');
    if (rapportSupport) {
      console.log('Logo present:', rapportSupport.logo ? 'Yes' : 'No');
      console.log('Signature present:', rapportSupport.signatureElectrique ? 'Yes' : 'No');
      console.log('Destinataire:', rapportSupport.destinataire ? 'Yes' : 'No');
    }

    // Get existing report data
    const reportData = await AuditMissionRapport.getMissionReport(missionId);
    console.log('[EnhancedAuditMissionRapport] Report data retrieved, rows:', reportData?.length);

    if (!reportData || reportData.length === 0) {
      return res.status(404).json({ message: 'No data found for the specified mission ID' });
    }

    if (format === 'pdf') {
      const doc = new PDFDocument({ margin: 50 });
      const chunks = [];

      doc.on('data', chunk => chunks.push(chunk));
      doc.on('end', () => {
        const pdfBuffer = Buffer.concat(chunks);
        res.setHeader('Content-Type', 'application/pdf');
        res.setHeader('Content-Disposition', `attachment; filename="rapport-mission-${missionId}.pdf"`);
        res.send(pdfBuffer);
      });

      // FRONT PAGE
      // Add a colored header background
      doc.rect(0, 0, 612, 120).fill('#2E86AB'); // Blue header

      // Add logo to top left if available
      if (rapportSupport?.logo) {
        addLogoToPage(doc, rapportSupport.logo, 50, 30, 120, 60);
      }

      // Add white text on blue background
      doc.fontSize(24).font('Helvetica-Bold').fillColor('#023a6e');
      doc.text('RAPPORT DE MISSION D\'AUDIT', 50, 180, { align: 'center', width: 500 });

      // Add decorative line
      doc.rect(50, 220, 500, 3).fill('#F18F01'); // Orange accent line

      doc.moveDown(3);

      // Mission details with colors
      doc.fontSize(18).font('Helvetica-Bold').fillColor('#2E86AB');
      doc.text('Mission:', 50, 280);
      doc.fontSize(16).font('Helvetica').fillColor('#333333');
      doc.text(escapeText(mission.name), 50, 310);
      doc.moveDown();

      // Date range with color
      doc.fontSize(18).font('Helvetica-Bold').fillColor('#2E86AB');
      doc.text('Période:', 50, 350);
      doc.fontSize(16).font('Helvetica').fillColor('#333333');
      const startDate = formatDate(mission.datedebut);
      const endDate = formatDate(mission.datefin);
      doc.text(`Du ${startDate} au ${endDate}`, 50, 380);
      doc.moveDown();

      // Destinataire with color
      if (rapportSupport?.destinataire) {
        doc.fontSize(18).font('Helvetica-Bold').fillColor('#2E86AB');
        doc.text('Destinataire:', 50, 420);
        doc.fontSize(16).font('Helvetica').fillColor('#333333');
        doc.text(escapeText(rapportSupport.destinataire), 50, 450);
        doc.moveDown();
      }

      // Principal Audité with color
      if (mission.principalAudite) {
        doc.fontSize(18).font('Helvetica-Bold').fillColor('#2E86AB');
        doc.text('Principal Audité:', 50, 490);
        doc.fontSize(16).font('Helvetica').fillColor('#333333');
        doc.text(escapeText(mission.principalAudite), 50, 520);
      }

      // Add decorative footer line
      doc.rect(50, 600, 500, 2).fill('#F18F01');

      // Add new page for sommaire
      doc.addPage();

      // Add colored header for sommaire
      doc.rect(0, 0, 612, 100).fill('#2E86AB'); // Blue header

      // Add logo to sommaire page
      if (rapportSupport?.logo) {
        addLogoToPage(doc, rapportSupport.logo, 50, 20, 120, 60);
      }

      // SOMMAIRE PAGE
      doc.fontSize(24).font('Helvetica-Bold').fillColor('#023a6e');
      doc.text('SOMMAIRE', 50, 130, { align: 'center', width: 500 });

      // Add decorative line
      doc.rect(50, 170, 500, 3).fill('#F18F01'); // Orange accent line
      doc.moveDown(3);

      // Sommaire items with alternating colors
      const sommaire = [
        '1. Introduction .................................................... 3',
        '2. Résumé ......................................................... 4',
        '   2.1 Objectif de la mission d\'Audit ........................... 4',
        '   2.2 Points forts .............................................. 4',
        '   2.3 Points faibles ............................................ 4',
        '3. Contexte ....................................................... 5',
        '   3.1 Managers opérationnels audités ........................... 5',
        '   3.2 Coûts de la mission d\'Audit .............................. 5',
        '4. Détails de la mission .......................................... 6',
        '   4.1 Équipe d\'audit ............................................ 6',
        '   4.2 Méthodologie .............................................. 6',
        '5. Constats et Recommandations .................................... 7',
        '6. Conclusion ..................................................... 8'
      ];

      let yPosition = 200;
      sommaire.forEach((item) => {
        // Alternate colors for better readability
        const isMainSection = !item.startsWith('   ');
        if (isMainSection) {
          doc.fontSize(14).font('Helvetica-Bold').fillColor('#2E86AB');
        } else {
          doc.fontSize(12).font('Helvetica').fillColor('#666666');
        }
        doc.text(item, 50, yPosition);
        yPosition += isMainSection ? 28 : 22;
      });

      // Add new page for the actual report content
      doc.addPage();

      // Add logo to content pages
      if (rapportSupport?.logo) {
        addLogoToPage(doc, rapportSupport.logo, 50, 50, 120, 60);
      }

      // Continue with existing report content
      const autresParticipants = [
        ...new Set(
          reportData
            .flatMap(row => [row.activity_responsable, row.constat_responsable])
            .filter(id => id !== null && id !== undefined)
        ),
      ].join(', ');

      const auditedElements = [
        reportData[0].risk_names,
        reportData[0].entity_names,
        reportData[0].control_names,
        reportData[0].incident_names,
        reportData[0].organizational_process_names,
      ]
        .filter(name => name)
        .join(', ');

      // Title with color
      doc.fontSize(20).font('Helvetica-Bold').fillColor('#2E86AB');
      doc.text('Rapport de Mission d\'Audit', 50, 120, { align: 'center', width: 500 });

      // Add decorative line
      doc.rect(50, 150, 500, 3).fill('#F18F01');
      doc.moveDown(3);

      // Section 1: Diffusion with colors
      doc.fontSize(16).font('Helvetica-Bold').fillColor('#2E86AB');
      doc.text('1. Diffusion', 50, 180);
      doc.moveDown(0.5);

      doc.fontSize(12).font('Helvetica-Bold').fillColor('#333333');
      doc.text(`Chef de mission: ${escapeText(reportData[0].chefmission)}`, 50, 210);
      doc.text(`Directeur d'audit: ${escapeText(reportData[0].directeuraudit)}`, 50, 230);
      doc.text(`Autre(s) Participant(s): ${escapeText(autresParticipants)}`, 50, 250);
      doc.moveDown();

      // Section 2: Résumé with colors
      doc.fontSize(16).font('Helvetica-Bold').fillColor('#2E86AB');
      doc.text('2. Résumé', 50, 290);
      doc.moveDown(0.5);

      doc.fontSize(12).font('Helvetica-Bold').fillColor('#333333');
      doc.text(`Mission d'audit: ${escapeText(reportData[0].mission_name)}`, 50, 320);
      doc.text(`Catégorie: ${escapeText(reportData[0].categorie)}`, 50, 340);
      doc.text(`Évaluation: ${escapeText(reportData[0].evaluation)}`, 50, 360);
      doc.moveDown();

      doc.fontSize(14).font('Helvetica-Bold').fillColor('#2E86AB');
      doc.text('2.1 Objectif de la mission d\'Audit', 50, 390);
      doc.fontSize(12).font('Helvetica').fillColor('#333333');
      doc.text(escapeText(reportData[0].objectif), 50, 410, { width: 500 });
      doc.moveDown();

      doc.fontSize(14).font('Helvetica-Bold').fillColor('#28A745');
      doc.text('2.2 Points forts', 50, 450);
      doc.fontSize(12).font('Helvetica').fillColor('#333333');
      doc.text(`La mission a permis de constater que : ${escapeText(reportData[0].pointfort)}`, 50, 470, { width: 500 });
      doc.moveDown();

      doc.fontSize(14).font('Helvetica-Bold').fillColor('#DC3545');
      doc.text('2.3 Points faibles', 50, 510);
      doc.fontSize(12).font('Helvetica').fillColor('#333333');
      doc.text(`Une faiblesse majeure a été relevée : ${escapeText(reportData[0].pointfaible)}`, 50, 530, { width: 500 });
      doc.moveDown();

      // Section 3: Contexte with color
      doc.fontSize(16).font('Helvetica-Bold').fillColor('#2E86AB');
      doc.text('3. Contexte', 50, 570);
      doc.moveDown(0.5);

      doc.fontSize(14).font('Helvetica-Bold').fillColor('#F18F01');
      doc.text('3.1 Managers opérationnels audités', 50, 600);
      doc.fontSize(12).font('Helvetica').fillColor('#333333');
      doc.text(`Élément audité: ${escapeText(auditedElements)}`, 50, 620, { width: 500 });
      doc.text(`Propriétaire: ${escapeText(reportData[0].recommendation_responsable)}`, 50, 640);

      // Add new page for more content if needed
      doc.addPage();

      // Add logo to new page
      if (rapportSupport?.logo) {
        addLogoToPage(doc, rapportSupport.logo, 50, 50, 120, 60);
      }

      doc.fontSize(14).font('Helvetica-Bold').fillColor('#2E86AB');
      doc.text('3.2 Coûts de la mission d\'Audit', 50, 120);
      doc.fontSize(12).font('Helvetica').fillColor('#333333');
      doc.text(`Charge de travail estimée (Heures): ${escapeText(String(reportData[0].chargedetravailestimee))}`, 50, 150);
      doc.text(`Charge de travail effective (Heures): ${escapeText(String(reportData[0].chargedetravaileffective))}`, 50, 170);
      doc.text(`Total des dépenses: ${escapeText(String(reportData[0].depenses))}`, 50, 190);

      // Add signature to the last page
      if (rapportSupport?.signatureElectrique) {
        // Add a section title for the signature
        doc.moveDown(4); // Move down to create space before signature
        doc.fontSize(12).font('Helvetica-Bold').fillColor('#2E86AB');
        doc.text('Signature', 400, null, { align: 'center', width: 150 });
        
        // Add the signature
        addSignatureToPage(doc, rapportSupport.signatureElectrique, 400, doc.y + 20, 150, 75);
        
        // Add the date under the signature
        doc.moveDown(5);
        doc.fontSize(10).font('Helvetica').fillColor('#666666');
        const currentDate = new Date().toLocaleDateString('fr-FR', {
          year: 'numeric',
          month: 'long',
          day: 'numeric'
        });
        doc.text(`Date: ${currentDate}`, 400, null, { align: 'center', width: 150 });
      }

      doc.end();
    } else {
      // Return JSON data including rapport support
      const enhancedData = {
        mission: {
          id: mission.id,
          name: mission.name,
          principalAudite: mission.principalAudite,
          datedebut: mission.datedebut,
          datefin: mission.datefin
        },
        rapportSupport: rapportSupport ? {
          logo: rapportSupport.logo,
          signatureElectrique: rapportSupport.signatureElectrique,
          destinataire: rapportSupport.destinataire
        } : null,
        reportData: reportData[0]
      };

      res.json(enhancedData);
    }
  } catch (error) {
    console.error('[EnhancedAuditMissionRapport] Error:', error);
    res.status(500).json({ message: 'Internal server error', error: error.message });
  }
};

exports.getMissionReport = async (req, res) => {
  const { missionId } = req.params;
  const { format } = req.query;

  // Set CORS headers immediately
  res.set({
    'Access-Control-Allow-Origin': 'http://localhost:5173',
    'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Requested-With, x-user-id',
    'Access-Control-Allow-Credentials': 'true'
  });

  console.log('[AuditMissionRapport] Processing request for mission:', missionId, 'format:', format);

  try {
    // Get mission data for enhanced features
    const mission = await AuditMission.findByPk(missionId);

    // Get rapport support data for enhanced features
    const rapportSupport = await AuditMissionRapportSupport.findOne({
      where: { auditMissionId: missionId }
    });

    console.log('Mission found:', mission ? 'Yes' : 'No');
    console.log('Rapport support found:', rapportSupport ? 'Yes' : 'No');
    if (rapportSupport) {
      console.log('Logo present:', rapportSupport.logo ? 'Yes' : 'No');
      console.log('Signature present:', rapportSupport.signatureElectrique ? 'Yes' : 'No');
      console.log('Destinataire:', rapportSupport.destinataire ? 'Yes' : 'No');
    }

    const reportData = await AuditMissionRapport.getMissionReport(missionId);
    console.log('[AuditMissionRapport] Report data retrieved, rows:', reportData?.length);

    if (!reportData || reportData.length === 0) {
      return res.status(404).json({ message: 'No data found for the specified mission ID' });
    }

    const autresParticipants = [
      ...new Set(
        reportData
          .flatMap(row => [row.activity_responsable, row.constat_responsable])
          .filter(id => id !== null && id !== undefined)
      ),
    ].join(', ');

    const auditedElements = [
      reportData[0].risk_names,
      reportData[0].entity_names,
      reportData[0].control_names,
      reportData[0].incident_names,
      reportData[0].organizational_process_names,
    ]
      .filter(name => name)
      .join(', ');

    if (format === 'pdf') {
      console.log('[AuditMissionRapport] Starting PDF generation for mission:', missionId);

      try {
        let missionName = reportData[0].mission_name || missionId;
        missionName = missionName.replace(/[^a-zA-Z0-9_-]/g, '_');

        // Set PDF headers before starting generation
        res.setHeader('Content-Type', 'application/pdf');
        res.setHeader('Content-Disposition', `attachment; filename=mission_audit_report_${missionName}.pdf`);

        const doc = new PDFDocument({ margin: 50 });

        // Pipe directly to response
        doc.pipe(res);

        console.log('[AuditMissionRapport] PDF document created and piped to response');

        // ===== ENHANCED FEATURES: FRONT PAGE =====
        if (mission && rapportSupport) {
          console.log('[AuditMissionRapport] Adding enhanced front page');

          // Add colored header background
          doc.rect(0, 0, 612, 120).fill('#2E86AB'); // Blue header

          // Add logo to top left if available
          if (rapportSupport.logo) {
            addLogoToPage(doc, rapportSupport.logo, 50, 30, 120, 60);
          }

          // Add white text on blue background
          doc.fontSize(24).font('Helvetica-Bold').fillColor('#023a6e');
          doc.text('RAPPORT DE MISSION D\'AUDIT', 50, 180, { align: 'center', width: 500 });

          // Add decorative line
          doc.rect(50, 220, 500, 3).fill('#F18F01'); // Orange accent line

          doc.moveDown(3);

          // Mission details with colors
          doc.fontSize(18).font('Helvetica-Bold').fillColor('#2E86AB');
          doc.text('Mission:', 50, 280);
          doc.fontSize(16).font('Helvetica').fillColor('#333333');
          doc.text(escapeText(mission.name), 50, 310);
          doc.moveDown();

          // Date range with color
          doc.fontSize(18).font('Helvetica-Bold').fillColor('#2E86AB');
          doc.text('Période:', 50, 350);
          doc.fontSize(16).font('Helvetica').fillColor('#333333');
          const startDate = formatDate(mission.datedebut);
          const endDate = formatDate(mission.datefin);
          doc.text(`Du ${startDate} au ${endDate}`, 50, 380);
          doc.moveDown();

          // Destinataire with color
          if (rapportSupport.destinataire) {
            doc.fontSize(18).font('Helvetica-Bold').fillColor('#2E86AB');
            doc.text('Destinataire:', 50, 420);
            doc.fontSize(16).font('Helvetica').fillColor('#333333');
            doc.text(escapeText(rapportSupport.destinataire), 50, 450);
            doc.moveDown();
          }

          // Principal Audité with color
          if (mission.principalAudite) {
            doc.fontSize(18).font('Helvetica-Bold').fillColor('#2E86AB');
            doc.text('Principal Audité:', 50, 490);
            doc.fontSize(16).font('Helvetica').fillColor('#333333');
            doc.text(escapeText(mission.principalAudite), 50, 520);
          }

          // Add decorative footer line
          doc.rect(50, 600, 500, 2).fill('#F18F01');

          // ===== ENHANCED FEATURES: SOMMAIRE PAGE =====
          doc.addPage();

          // Add colored header for sommaire
          doc.rect(0, 0, 612, 100).fill('#2E86AB'); // Blue header

          // Add logo to sommaire page
          if (rapportSupport.logo) {
            addLogoToPage(doc, rapportSupport.logo, 50, 20, 120, 60);
          }

          // SOMMAIRE PAGE
          doc.fontSize(24).font('Helvetica-Bold').fillColor('#023a6e');
          doc.text('SOMMAIRE', 50, 130, { align: 'center', width: 500 });

          // Add decorative line
          doc.rect(50, 170, 500, 3).fill('#F18F01'); // Orange accent line
          doc.moveDown(3);

          // Sommaire items with alternating colors
          const sommaire = [
            '1. Introduction .................................................... 3',
            '2. Résumé ......................................................... 4',
            '   2.1 Objectif de la mission d\'Audit ........................... 4',
            '   2.2 Points forts .............................................. 4',
            '   2.3 Points faibles ............................................ 4',
            '3. Contexte ....................................................... 5',
            '   3.1 Managers opérationnels audités ........................... 5',
            '   3.2 Coûts de la mission d\'Audit .............................. 5',
            '4. Détails de la mission .......................................... 6',
            '   4.1 Équipe d\'audit ............................................ 6',
            '   4.2 Méthodologie .............................................. 6',
            '5. Constats et Recommandations .................................... 7',
            '   5.1 Constats urgents ......................................... 7',
            '   5.2 Détails des constats et recommandations .................. 8',
            '6. Signature ..................................................... 9'
          ];

          let yPosition = 200;
          sommaire.forEach((item) => {
            // Alternate colors for better readability
            const isMainSection = !item.startsWith('   ');
            if (isMainSection) {
              doc.fontSize(14).font('Helvetica-Bold').fillColor('#2E86AB');
            } else {
              doc.fontSize(12).font('Helvetica').fillColor('#666666');
            }
            doc.text(item, 50, yPosition);
            yPosition += isMainSection ? 28 : 22;
          });

          // Add new page for the original content
          doc.addPage();

          // Add logo to content pages
          if (rapportSupport.logo) {
            addLogoToPage(doc, rapportSupport.logo, 50, 50, 120, 60);
          }

          console.log('[AuditMissionRapport] Enhanced pages added, continuing with original content');
        }

      // ===== ORIGINAL CONTENT (Enhanced with colors) =====
      // Title with color (adjust position if enhanced features were added)
      const titleY = (mission && rapportSupport) ? 120 : 50;
      doc.fontSize(20).font('Helvetica-Bold').fillColor('#023a6e');
      doc.text('Rapport de Mission d\'Audit', 50, titleY, { align: 'center', width: 500 });

      // Add decorative line
      doc.rect(50, titleY + 30, 500, 3).fill('#F18F01');
      doc.moveDown(3);

      // Section 1: Diffusion with colors
      doc.fontSize(16).font('Helvetica-Bold').fillColor('#2E86AB');
      doc.text('1. Diffusion');
      doc.moveDown(0.5);

      doc.fontSize(12).font('Helvetica-Bold').fillColor('#333333');
      doc.text(`Chef de mission: `);
      doc.fontSize(12).font('Helvetica').fillColor('#666666');
      doc.text(`${escapeText(reportData[0].chefmission)}`, { continued: false });

      doc.fontSize(12).font('Helvetica-Bold').fillColor('#333333');
      doc.text(`Directeur d'audit: `);
      doc.fontSize(12).font('Helvetica').fillColor('#666666');
      doc.text(`${escapeText(reportData[0].directeuraudit)}`, { continued: false });

      doc.fontSize(12).font('Helvetica-Bold').fillColor('#333333');
      doc.text(`Autre(s) Participant(s): `);
      doc.fontSize(12).font('Helvetica').fillColor('#666666');
      doc.text(`${escapeText(autresParticipants)}`, { continued: false });
      doc.moveDown();

      // Section 2: Résumé with colors
      doc.fontSize(16).font('Helvetica-Bold').fillColor('#2E86AB');
      doc.text('2. Résumé');
      doc.moveDown(0.5);

      doc.fontSize(12).font('Helvetica-Bold').fillColor('#333333');
      doc.text(`Mission d'audit: `);
      doc.fontSize(12).font('Helvetica').fillColor('#666666');
      doc.text(`${escapeText(reportData[0].mission_name)}`, { continued: false });

      doc.fontSize(12).font('Helvetica-Bold').fillColor('#333333');
      doc.text(`Catégorie: `);
      doc.fontSize(12).font('Helvetica').fillColor('#666666');
      doc.text(`${escapeText(reportData[0].categorie)}`, { continued: false });

      doc.fontSize(12).font('Helvetica-Bold').fillColor('#333333');
      doc.text(`Évaluation: `);
      doc.fontSize(12).font('Helvetica').fillColor('#666666');
      doc.text(`${escapeText(reportData[0].evaluation)}`, { continued: false });
      doc.moveDown();

      doc.fontSize(14).font('Helvetica-Bold').fillColor('#2E86AB');
      doc.text('2.1 Objectif de la mission d\'Audit');
      doc.fontSize(12).font('Helvetica').fillColor('#333333');
      doc.text(escapeText(reportData[0].objectif), { indent: 20 });
      doc.moveDown();

      doc.fontSize(14).font('Helvetica-Bold').fillColor('#2E86AB'); 
      doc.text('2.2 Points forts');
      doc.fontSize(12).font('Helvetica').fillColor('#333333');
      doc.text(`La mission a permis de constater que : ${escapeText(reportData[0].pointfort)}`, { indent: 20 });
      doc.moveDown();

      doc.fontSize(14).font('Helvetica-Bold').fillColor('#2E86AB'); 
      doc.text('2.3 Points faibles');
      doc.fontSize(12).font('Helvetica').fillColor('#333333');
      doc.text(`Une faiblesse majeure a été relevée : ${escapeText(reportData[0].pointfaible)}`, { indent: 20 });
      doc.moveDown();

      // Section 3: Contexte with color
      doc.fontSize(16).font('Helvetica-Bold').fillColor('#2E86AB');
      doc.text('3. Contexte');
      doc.moveDown(0.5);

      doc.fontSize(12).font('Helvetica-Bold').text('3.1 Managers opérationnels audités');
      doc.moveDown(0.5);
      doc.fontSize(12).font('Helvetica').fillColor('#333333');
      doc.text(`Élément audité: ${escapeText(auditedElements)}`);
      doc.text(`Propriétaire: ${escapeText(reportData[0].recommendation_responsable)}`);
      doc.moveDown();

      doc.fillColor('#2E86AB').fontSize(12).font('Helvetica-Bold').text('3.2 Coûts de la mission d\'Audit');
      doc.moveDown(0.5);
      doc.fillColor('#333333').fontSize(12).font('Helvetica');
      doc.text(`Charge de travail estimée (Heures): ${escapeText(String(reportData[0].chargedetravailestimee))}`);
      doc.text(`Charge de travail effective (Heures): ${escapeText(String(reportData[0].chargedetravaileffective))}`);
      doc.text(`Total des dépenses: ${escapeText(String(reportData[0].depenses))}`);
      doc.moveDown();

      doc.fillColor('#2E86AB').fontSize(12).font('Helvetica-Bold').text('3.3 Ressources de la mission d\'Audit');
      doc.moveDown(0.5);
      doc.fillColor('#333333').fontSize(12).font('Helvetica');
      doc.text(`Chef de mission: ${escapeText(reportData[0].chefmission)}`);
      doc.text(`Principal audité: ${escapeText(reportData[0].principalAudite)}`);
      doc.text(`Audité(s): ${escapeText(auditedElements)}`);
      doc.text(`Autre(s) Participant(s): ${escapeText(autresParticipants)}`);
      doc.moveDown();

      // Section 4: Avis
      doc.fillColor('#2E86AB').fontSize(14).font('Helvetica-Bold').text('4. Avis concernant la mission d\'Audit', { underline: true });
      doc.moveDown(0.5);
      doc.fillColor('#333333').fontSize(12).font('Helvetica');
      doc.text(`Les objectifs de la mission d'Audit sont : ${escapeText(reportData[0].objectif)}`);
      doc.text('Les sections suivantes présentent les détails du résultat de la mission d\'Audit.');
      doc.moveDown();

      doc.fillColor('#2E86AB').fontSize(12).font('Helvetica-Bold').text('4.1 Points forts');
      doc.fillColor('#333333').fontSize(12).font('Helvetica').text(`La mission a permis de constater que : ${escapeText(reportData[0].pointfort)}`, { indent: 20 });
      doc.moveDown();

      doc.fillColor('#2E86AB').fontSize(12).font('Helvetica-Bold').text('4.2 Points faibles');
      doc.fillColor('#333333').fontSize(12).font('Helvetica').text(`Une faiblesse majeure a été relevée : ${escapeText(reportData[0].pointfaible)}`, { indent: 20 });
      doc.moveDown();

      // Section 5: Constats
      doc.fillColor('#2E86AB').fontSize(14).font('Helvetica-Bold').text('5. Constats', { underline: true });
      doc.moveDown(0.5);

      doc.fontSize(12).font('Helvetica-Bold').text('5.1 Constats urgents');
      doc.moveDown(0.5);
      const urgentFindings = reportData.filter(row => row.constat_impact === 'très fort');
      if (urgentFindings.length === 0) {
        doc.fillColor('#333333').fontSize(12).font('Helvetica').text('Aucun constat urgent trouvé.');
      } else {
        urgentFindings.forEach(row => {
          doc.fillColor('#333333').fontSize(12).font('Helvetica').text(`Constat: ${escapeText(row.constat_name)}`);
          doc.text(`Impact: ${escapeText(row.constat_impact)}`);
          doc.text(`Risque(s): ${escapeText(row.constat_risks)}`);
          doc.moveDown(0.5);
        });
      }
      doc.moveDown();

      doc.fillColor('#2E86AB').fontSize(12).font('Helvetica-Bold').text('5.2 Détails des constats et recommandations');
      doc.moveDown(0.5);
      const findings = reportData.filter(row => row.constat_name);
      findings.forEach(row => {
        doc.fillColor('#333333').fontSize(12).font('Helvetica');
        doc.text(`Constat: ${escapeText(row.constat_name)}`);
        doc.text(`Impact: ${escapeText(row.constat_impact)}`);
        doc.text(`Risque(s): ${escapeText(row.constat_risks)}`);
        doc.text(`Recommandation: ${escapeText(row.recommendation_name)}`);
        doc.text(`Détails: ${escapeText(row.recommendation_details)}`);
        doc.text(`Propriétaire: ${escapeText(row.recommendation_responsable)}`);
        doc.text(`Date de fin: ${escapeText(row.datefin)}`);
        doc.moveDown(0.5);
      });

      // Add signature section
      doc.moveDown(4);
      
      // Section 6: Signature
      doc.fillColor('#2E86AB').fontSize(14).font('Helvetica-Bold').text('6. Signature', { align: 'left' });
      doc.moveDown();
      
      // Add current date
      const currentDate = new Date().toLocaleDateString('fr-FR');
      doc.fillColor('#333333').fontSize(12).font('Helvetica').text(`Date: ${currentDate}`, { align: 'right' });
      doc.moveDown(2);

      // Add electronic signature if available
      if (rapportSupport && rapportSupport.signatureElectrique) {
        addSignatureToPage(doc, rapportSupport.signatureElectrique, 400, null, 150, 75);
      }

      // Finalize the PDF document
        console.log('[AuditMissionRapport] Finalizing PDF document');
        doc.end();

      } catch (pdfError) {
        console.error('[AuditMissionRapport] PDF generation error:', pdfError);
        res.status(500).json({ message: 'Failed to generate PDF', error: pdfError.message });
      }

    } else if (format === 'docx') {
      // Get and sanitize mission name for filename
      let missionName = reportData[0].mission_name || missionId;
      missionName = missionName.replace(/[^a-zA-Z0-9_-]/g, '_');
      const filename = `mission_audit_report_${missionName}.docx`;
      const doc = new Document({
        sections: [
          {
            properties: {
              page: {
                margin: {
                  top: 720,
                  right: 720,
                  bottom: 720,
                  left: 720,
                },
              },
            },
            headers: {
              default: new Header({
                children: [
                  // Add logo with robust error handling
                  ...(rapportSupport?.logo ? (() => {
                    try {
                      const logoBuffer = processImageForDocx(rapportSupport.logo, 'Logo');
                      if (logoBuffer) {
                        console.log('[DOCX] Adding logo to header');
                        return [
                          new Paragraph({
                            children: [
                              new ImageRun({
                                data: logoBuffer,
                                transformation: {
                                  width: 120,
                                  height: 60,
                                },
                              }),
                            ],
                            alignment: AlignmentType.LEFT,
                            spacing: {
                              after: 200,
                            },
                          }),
                        ];
                      } else {
                        console.warn('[DOCX] Logo processing failed, using text fallback');
                        return [
                          new Paragraph({
                            children: [
                              new TextRun({
                                text: "[LOGO]",
                                bold: true,
                                size: 16,
                                color: '666666',
                              }),
                            ],
                            alignment: AlignmentType.LEFT,
                          }),
                        ];
                      }
                    } catch (error) {
                      console.error('[DOCX] Error adding logo:', error);
                      return [
                        new Paragraph({
                          children: [
                            new TextRun({
                              text: "[LOGO]",
                              bold: true,
                              size: 16,
                              color: '666666',
                            }),
                          ],
                          alignment: AlignmentType.LEFT,
                        }),
                      ];
                    }
                  })() : []),
                  // Header title
                  new Paragraph({
                    children: [
                      new TextRun({
                        text: "Rapport de Mission d'Audit",
                        bold: true,
                        size: 20,
                        color: '2E86AB',
                      }),
                    ],
                    alignment: AlignmentType.CENTER,
                  }),
                ],
              }),
            },
            footers: {
              default: new Footer({
                children: [
                  new Paragraph({
                    children: [
                      new TextRun({
                        text: new Date().toLocaleDateString('fr-FR'),
                        size: 20,
                      }),
                    ],
                    alignment: AlignmentType.RIGHT,
                  }),
                ],
              }),
            },
            children: [
              // ===== ENHANCED FEATURES: FRONT PAGE CONTENT =====
              ...(mission && rapportSupport ? [
                new Paragraph({
                  children: [
                    new TextRun({
                      text: 'RAPPORT DE MISSION D\'AUDIT',
                      bold: true,
                      size: 36,
                      color: '023a6e',
                    }),
                  ],
                  alignment: 'center',
                }),
                new Paragraph(''), // Empty line
                new Paragraph({
                  children: [
                    new TextRun({
                      text: 'Mission: ',
                      bold: true,
                      size: 24,
                      color: '2E86AB',
                    }),
                    new TextRun({
                      text: mission.name || 'N/A',
                      size: 20,
                      color: '333333',
                    }),
                  ],
                }),
                new Paragraph({
                  children: [
                    new TextRun({
                      text: 'Période: ',
                      bold: true,
                      size: 20,
                      color: '2E86AB',
                    }),
                    new TextRun({
                      text: `Du ${formatDate(mission.datedebut)} au ${formatDate(mission.datefin)}`,
                      size: 16,
                      color: '333333',
                    }),
                  ],
                }),
                ...(rapportSupport.destinataire ? [
                  new Paragraph({
                    children: [
                      new TextRun({
                        text: 'Destinataire: ',
                        bold: true,
                        size: 20,
                        color: '2E86AB',
                      }),
                      new TextRun({
                        text: rapportSupport.destinataire,
                        size: 16,
                        color: '333333',
                      }),
                    ],
                  }),
                ] : []),
                ...(mission.principalAudite ? [
                  new Paragraph({
                    children: [
                      new TextRun({
                        text: 'Principal Audité: ',
                        bold: true,
                        size: 20,
                        color: '2E86AB',
                      }),
                      new TextRun({
                        text: mission.principalAudite,
                        size: 16,
                        color: '333333',
                      }),
                    ],
                  }),
                ] : []),
                new Paragraph(''), // Page break
                new Paragraph(''),
                // ===== SOMMAIRE =====
                new Paragraph({
                  children: [
                    new TextRun({
                      text: 'SOMMAIRE',
                      bold: true,
                      size: 28,
                      color: '023a6e',
                    }),
                  ],
                  alignment: 'center',
                }),
                new Paragraph(''),
                new Paragraph({
                  children: [
                    new TextRun({
                      text: '1. Introduction',
                      bold: true,
                      color: '2E86AB',
                    }),
                  ],
                }),
                new Paragraph({
                  children: [
                    new TextRun({
                      text: '2. Résumé',
                      bold: true,
                      color: '2E86AB',
                    }),
                  ],
                }),
                new Paragraph({
                  children: [
                    new TextRun({
                      text: '   2.1 Objectif de la mission d\'Audit',
                      color: '666666',
                    }),
                  ],
                }),
                new Paragraph({
                  children: [
                    new TextRun({
                      text: '   2.2 Points forts',
                      color: '666666',
                    }),
                  ],
                }),
                new Paragraph({
                  children: [
                    new TextRun({
                      text: '   2.3 Points faibles',
                      color: '666666',
                    }),
                  ],
                }),
                new Paragraph({
                  children: [
                    new TextRun({
                      text: '3. Contexte',
                      bold: true,
                      color: '2E86AB',
                    }),
                  ],
                }),
                new Paragraph({
                  children: [
                    new TextRun({
                      text: '4. Détails de la mission',
                      bold: true,
                      color: '2E86AB',
                    }),
                  ],
                }),
                new Paragraph({
                  children: [
                    new TextRun({
                      text: '5. Constats et Recommandations',
                      bold: true,
                      color: '2E86AB',
                    }),
                  ],
                }),
                new Paragraph({
                  children: [
                    new TextRun({
                      text: '6. Conclusion',
                      bold: true,
                      color: '2E86AB',
                    }),
                  ],
                }),
                new Paragraph(''), // Page break before content
                new Paragraph(''),
              ] : []),
              // ===== ORIGINAL CONTENT (Enhanced with colors) =====
              new Paragraph({
                children: [
                  new TextRun({
                    text: 'Rapport de Mission d\'Audit',
                    bold: true,
                    size: 32,
                    color: '023a6e', // Blue color
                  }),
                ],
                alignment: 'center',
              }),
              new Paragraph({
                children: [
                  new TextRun({
                    text: '1. Diffusion',
                    bold: true,
                    size: 24,
                    color: '2E86AB', // Blue color
                  }),
                ],
              }),
              new Paragraph({
                children: [
                  new TextRun({
                    text: 'Chef de mission: ',
                    bold: true,
                    color: '333333',
                  }),
                  new TextRun({
                    text: reportData[0].chefmission || 'N/A',
                    color: '666666',
                  }),
                ],
              }),
              new Paragraph({
                children: [
                  new TextRun({
                    text: 'Directeur d\'audit: ',
                    bold: true,
                    color: '333333',
                  }),
                  new TextRun({
                    text: reportData[0].directeuraudit || 'N/A',
                    color: '666666',
                  }),
                ],
              }),
              new Paragraph({
                children: [
                  new TextRun({
                    text: 'Autre(s) Participant(s): ',
                    bold: true,
                    color: '333333',
                  }),
                  new TextRun({
                    text: autresParticipants || 'N/A',
                    color: '666666',
                  }),
                ],
              }),
              new Paragraph({
                children: [
                  new TextRun({
                    text: '2. Résumé',
                    bold: true,
                    size: 24,
                    color: '2E86AB', // Blue color
                  }),
                ],
              }),
              new Paragraph({
                children: [
                  new TextRun({
                    text: 'Mission d\'audit: ',
                    bold: true,
                    color: '333333',
                  }),
                  new TextRun({
                    text: reportData[0].mission_name || 'N/A',
                    color: '666666',
                  }),
                ],
              }),
              new Paragraph({
                children: [
                  new TextRun({
                    text: 'Catégorie: ',
                    bold: true,
                    color: '333333',
                  }),
                  new TextRun({
                    text: reportData[0].categorie || 'N/A',
                    color: '666666',
                  }),
                ],
              }),
              new Paragraph({
                children: [
                  new TextRun({
                    text: 'Évaluation: ',
                    bold: true,
                    color: '333333',
                  }),
                  new TextRun({
                    text: reportData[0].evaluation || 'N/A',
                    color: '666666',
                  }),
                ],
              }),
              new Paragraph({
                text: '2.1 Objectif de la mission d\'Audit',
                heading: HeadingLevel.HEADING_2,
              }),
              new Paragraph(reportData[0].objectif || 'N/A'),
              new Paragraph({
                text: '2.2 Points forts',
                heading: HeadingLevel.HEADING_2,
              }),
              new Paragraph(`La mission a permis de constater que : ${reportData[0].pointfort || 'N/A'}`),
              new Paragraph({
                text: '2.3 Points faibles',
                heading: HeadingLevel.HEADING_2,
              }),
              new Paragraph(`Une faiblesse majeure a été relevée : ${reportData[0].pointfaible || 'N/A'}`),
              new Paragraph({
                text: '3. Contexte',
                heading: HeadingLevel.HEADING_1,
              }),
              new Paragraph({
                text: '3.1 Managers opérationnels audités',
                heading: HeadingLevel.HEADING_2,
              }),
              new Table({
                width: {
                  size: 100,
                  type: WidthType.PERCENTAGE,
                },
                borders: {
                  top: { style: BorderStyle.SINGLE, size: 1 },
                  bottom: { style: BorderStyle.SINGLE, size: 1 },
                  left: { style: BorderStyle.SINGLE, size: 1 },
                  right: { style: BorderStyle.SINGLE, size: 1 },
                },
                rows: [
                  new TableRow({
                    children: [
                      new TableCell({
                        width: { size: 50, type: WidthType.PERCENTAGE },
                        children: [new Paragraph({ text: 'Élément audité', bold: true })],
                      }),
                      new TableCell({
                        width: { size: 50, type: WidthType.PERCENTAGE },
                        children: [new Paragraph({ text: 'Propriétaire', bold: true })],
                      }),
                    ],
                  }),
                  new TableRow({
                    children: [
                      new TableCell({
                        children: [new Paragraph(auditedElements || 'N/A')],
                      }),
                      new TableCell({
                        children: [new Paragraph(reportData[0].recommendation_responsable || 'N/A')],
                      }),
                    ],
                  }),
                ],
              }),
              new Paragraph({
                text: '3.2 Coûts de la mission d\'Audit',
                heading: HeadingLevel.HEADING_2,
              }),
              new Table({
                width: {
                  size: 100,
                  type: WidthType.PERCENTAGE,
                },
                borders: {
                  top: { style: BorderStyle.SINGLE, size: 1 },
                  bottom: { style: BorderStyle.SINGLE, size: 1 },
                  left: { style: BorderStyle.SINGLE, size: 1 },
                  right: { style: BorderStyle.SINGLE, size: 1 },
                },
                rows: [
                  new TableRow({
                    children: [
                      new TableCell({
                        width: { size: 50, type: WidthType.PERCENTAGE },
                        children: [new Paragraph({ text: 'Description', bold: true })],
                      }),
                      new TableCell({
                        width: { size: 50, type: WidthType.PERCENTAGE },
                        children: [new Paragraph({ text: 'Valeur', bold: true })],
                      }),
                    ],
                  }),
                  new TableRow({
                    children: [
                      new TableCell({
                        children: [new Paragraph('Charge de travail estimée (Heures)')],
                      }),
                      new TableCell({
                        children: [new Paragraph(String(reportData[0].chargedetravailestimee || 'N/A'))],
                      }),
                    ],
                  }),
                  new TableRow({
                    children: [
                      new TableCell({
                        children: [new Paragraph('Charge de travail effective (Heures)')],
                      }),
                      new TableCell({
                        children: [new Paragraph(String(reportData[0].chargedetravaileffective || 'N/A'))],
                      }),
                    ],
                  }),
                  new TableRow({
                    children: [
                      new TableCell({
                        children: [new Paragraph('Total des dépenses')],
                      }),
                      new TableCell({
                        children: [new Paragraph(String(reportData[0].depenses || 'N/A'))],
                      }),
                    ],
                  }),
                ],
              }),
              new Paragraph({
                text: '3.3 Ressources de la mission d\'Audit',
                heading: HeadingLevel.HEADING_2,
              }),
              new Table({
                width: {
                  size: 100,
                  type: WidthType.PERCENTAGE,
                },
                borders: {
                  top: { style: BorderStyle.SINGLE, size: 1 },
                  bottom: { style: BorderStyle.SINGLE, size: 1 },
                  left: { style: BorderStyle.SINGLE, size: 1 },
                  right: { style: BorderStyle.SINGLE, size: 1 },
                },
                rows: [
                  new TableRow({
                    children: [
                      new TableCell({
                        width: { size: 50, type: WidthType.PERCENTAGE },
                        children: [new Paragraph({ text: 'Rôle', bold: true })],
                      }),
                      new TableCell({
                        width: { size: 50, type: WidthType.PERCENTAGE },
                        children: [new Paragraph({ text: 'Nom', bold: true })],
                      }),
                    ],
                  }),
                  new TableRow({
                    children: [
                      new TableCell({
                        children: [new Paragraph('Chef de mission')],
                      }),
                      new TableCell({
                        children: [new Paragraph(reportData[0].chefmission || 'N/A')],
                      }),
                    ],
                  }),
                  new TableRow({
                    children: [
                      new TableCell({
                        children: [new Paragraph('Principal audité')],
                      }),
                      new TableCell({
                        children: [new Paragraph(reportData[0].principalAudite || 'N/A')],
                      }),
                    ],
                  }),
                  new TableRow({
                    children: [
                      new TableCell({
                        children: [new Paragraph('Audité(s)')],
                      }),
                      new TableCell({
                        children: [new Paragraph(auditedElements || 'N/A')],
                      }),
                    ],
                  }),
                  new TableRow({
                    children: [
                      new TableCell({
                        children: [new Paragraph('Autre(s) Participant(s)')],
                      }),
                      new TableCell({
                        children: [new Paragraph(autresParticipants || 'N/A')],
                      }),
                    ],
                  }),
                ],
              }),
              new Paragraph({
                text: '4. Avis concernant la mission d\'Audit',
                heading: HeadingLevel.HEADING_1,
              }),
              new Paragraph(`Les objectifs de la mission d'Audit sont : ${reportData[0].objectif || 'N/A'}`),
              new Paragraph('Les sections suivantes présentent les détails du résultat de la mission d\'Audit.'),
              new Paragraph({
                text: '4.1 Points forts',
                heading: HeadingLevel.HEADING_2,
              }),
              new Paragraph(`La mission a permis de constater que : ${reportData[0].pointfort || 'N/A'}`),
              new Paragraph({
                text: '4.2 Points faibles',
                heading: HeadingLevel.HEADING_2,
              }),
              new Paragraph(`Une faiblesse majeure a été relevée : ${reportData[0].pointfaible || 'N/A'}`),
              new Paragraph({
                text: '5. Constats',
                heading: HeadingLevel.HEADING_1,
              }),
              new Paragraph({
                text: '5.1 Constats urgents',
                heading: HeadingLevel.HEADING_2,
              }),
              new Table({
                width: {
                  size: 100,
                  type: WidthType.PERCENTAGE,
                },
                borders: {
                  top: { style: BorderStyle.SINGLE, size: 1 },
                  bottom: { style: BorderStyle.SINGLE, size: 1 },
                  left: { style: BorderStyle.SINGLE, size: 1 },
                  right: { style: BorderStyle.SINGLE, size: 1 },
                },
                rows: [
                  new TableRow({
                    children: [
                      new TableCell({
                        width: { size: 33, type: WidthType.PERCENTAGE },
                        children: [new Paragraph({ text: 'Constat', bold: true })],
                      }),
                      new TableCell({
                        width: { size: 33, type: WidthType.PERCENTAGE },
                        children: [new Paragraph({ text: 'Impact', bold: true })],
                      }),
                      new TableCell({
                        width: { size: 34, type: WidthType.PERCENTAGE },
                        children: [new Paragraph({ text: 'Risque(s)', bold: true })],
                      }),
                    ],
                  }),
                  ...reportData
                    .filter(row => row.constat_impact === 'très fort')
                    .map(
                      row =>
                        new TableRow({
                          children: [
                            new TableCell({
                              children: [new Paragraph(row.constat_name || 'N/A')],
                            }),
                            new TableCell({
                              children: [new Paragraph(row.constat_impact || 'N/A')],
                            }),
                            new TableCell({
                              children: [new Paragraph(row.constat_risks || 'N/A')],
                            }),
                          ],
                        })
                    ),
                  ...(reportData.filter(row => row.constat_impact === 'très fort').length === 0
                    ? [
                        new TableRow({
                          children: [
                            new TableCell({
                              children: [new Paragraph('Aucun constat urgent trouvé.')],
                              columnSpan: 3,
                            }),
                          ],
                        }),
                      ]
                    : []),
                ],
              }),
              new Paragraph({
                text: '5.2 Détails des constats et recommandations',
                heading: HeadingLevel.HEADING_2,
              }),
              new Table({
                width: {
                  size: 100,
                  type: WidthType.PERCENTAGE,
                },
                borders: {
                  top: { style: BorderStyle.SINGLE, size: 1 },
                  bottom: { style: BorderStyle.SINGLE, size: 1 },
                  left: { style: BorderStyle.SINGLE, size: 1 },
                  right: { style: BorderStyle.SINGLE, size: 1 },
                },
                rows: [
                  new TableRow({
                    children: [
                      new TableCell({
                        width: { size: 14, type: WidthType.PERCENTAGE },
                        children: [new Paragraph({ text: 'Constat', bold: true })],
                      }),
                      new TableCell({
                        width: { size: 14, type: WidthType.PERCENTAGE },
                        children: [new Paragraph({ text: 'Impact', bold: true })],
                      }),
                      new TableCell({
                        width: { size: 14, type: WidthType.PERCENTAGE },
                        children: [new Paragraph({ text: 'Risque(s)', bold: true })],
                      }),
                      new TableCell({
                        width: { size: 14, type: WidthType.PERCENTAGE },
                        children: [new Paragraph({ text: 'Recommandation', bold: true })],
                      }),
                      new TableCell({
                        width: { size: 14, type: WidthType.PERCENTAGE },
                        children: [new Paragraph({ text: 'Détails', bold: true })],
                      }),
                      new TableCell({
                        width: { size: 14, type: WidthType.PERCENTAGE },
                        children: [new Paragraph({ text: 'Propriétaire', bold: true })],
                      }),
                      new TableCell({
                        width: { size: 16, type: WidthType.PERCENTAGE },
                        children: [new Paragraph({ text: 'Date de fin', bold: true })],
                      }),
                    ],
                  }),
                  ...reportData
                    .filter(row => row.constat_name)
                    .map(
                      row =>
                        new TableRow({
                          children: [
                            new TableCell({
                              children: [new Paragraph(row.constat_name || 'N/A')],
                            }),
                            new TableCell({
                              children: [new Paragraph(row.constat_impact || 'N/A')],
                            }),
                            new TableCell({
                              children: [new Paragraph(row.constat_risks || 'N/A')],
                            }),
                            new TableCell({
                              children: [new Paragraph(row.recommendation_name || 'N/A')],
                            }),
                            new TableCell({
                              children: [new Paragraph(row.recommendation_details || 'N/A')],
                            }),
                            new TableCell({
                              children: [new Paragraph(row.recommendation_responsable || 'N/A')],
                            }),
                            new TableCell({
                              children: [new Paragraph(row.datefin || 'N/A')],
                            }),
                          ],
                        })
                    ),
                ],
              }),
              // Add signature section
              new Paragraph({ text: '', break: true }),
              new Paragraph({
                text: '6. Signature',
                heading: HeadingLevel.HEADING_1,
                alignment: AlignmentType.LEFT,
              }),
              new Paragraph({
                children: [
                  new TextRun({
                    text: `Date: ${new Date().toLocaleDateString('fr-FR')}`,
                    size: 24,
                  }),
                ],
                alignment: AlignmentType.RIGHT,
              }),
              // Add signature with robust error handling
              ...(rapportSupport?.signatureElectrique ? (() => {
                try {
                  const signatureBuffer = processImageForDocx(rapportSupport.signatureElectrique, 'Signature');
                  if (signatureBuffer) {
                    console.log('[DOCX] Adding signature to document');
                    return [
                      new Paragraph({
                        children: [
                          new TextRun({
                            text: 'Signature:',
                            bold: true,
                            size: 20,
                            color: '2E86AB',
                          }),
                        ],
                        alignment: AlignmentType.RIGHT,
                        spacing: { before: 300, after: 100 },
                      }),
                      new Paragraph({
                        children: [
                          new ImageRun({
                            data: signatureBuffer,
                            transformation: {
                              width: 150,
                              height: 75,
                            },
                          }),
                        ],
                        alignment: AlignmentType.RIGHT,
                        spacing: { after: 200 },
                      }),
                    ];
                  } else {
                    console.warn('[DOCX] Signature processing failed, using text fallback');
                    return [
                      new Paragraph({
                        children: [
                          new TextRun({
                            text: 'Signature électronique disponible',
                            italic: true,
                            color: '666666',
                          }),
                        ],
                        alignment: AlignmentType.RIGHT,
                        spacing: { before: 300, after: 300 },
                      }),
                    ];
                  }
                } catch (error) {
                  console.error('[DOCX] Error adding signature:', error);
                  return [
                    new Paragraph({
                      children: [
                        new TextRun({
                          text: 'Signature électronique disponible',
                          italic: true,
                          color: '666666',
                        }),
                      ],
                      alignment: AlignmentType.RIGHT,
                      spacing: { before: 300, after: 300 },
                    }),
                  ];
                }
              })() : []),
            ],
          },
        ],
      });

      try {
        console.log('[AuditMissionRapport] Starting DOCX generation...');

        // Basic document validation
        if (!doc) {
          console.error('[AuditMissionRapport] Document object is null');
          throw new Error('Document object is null');
        }

        // Create the buffer
        const buffer = await Packer.toBuffer(doc);
        
        console.log('[AuditMissionRapport] DOCX buffer size:', buffer.length);
        
        // Basic buffer validation
        if (!buffer || buffer.length === 0) {
          console.error('[AuditMissionRapport] Empty buffer generated');
          throw new Error('Empty buffer generated');
        }
        if (!buffer || buffer.length === 0) {
          throw new Error('Generated DOCX buffer is empty');
        }

        // Set headers before sending
        res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document');
        res.setHeader('Content-Disposition', `attachment; filename=${filename}`);
        
        // Send as buffer
        res.end(buffer);
      } catch (docxError) {
        console.error('[AuditMissionRapport] DOCX generation error:', docxError);
        res.status(500).json({ message: 'Failed to generate DOCX', error: docxError.message });
      }
    } else {
      // Return JSON data including enhanced features
      const enhancedData = {
        reportData: reportData[0],
        // Add enhanced features data
        mission: mission ? {
          id: mission.id,
          name: mission.name,
          principalAudite: mission.principalAudite,
          datedebut: mission.datedebut,
          datefin: mission.datefin
        } : null,
        rapportSupport: rapportSupport ? {
          id: rapportSupport.id,
          logo: rapportSupport.logo ? 'Present' : 'Not provided', // Don't send full base64 in JSON
          signatureElectrique: rapportSupport.signatureElectrique ? 'Present' : 'Not provided',
          destinataire: rapportSupport.destinataire
        } : null,
        enhancedFeaturesAvailable: !!(mission && rapportSupport)
      };

      res.status(200).json(enhancedData);
    }
  } catch (error) {
    console.error('[AuditMissionRapport] Error generating report:', error);
    res.status(500).json({ message: 'Error generating report', error: error.message });
  }
};